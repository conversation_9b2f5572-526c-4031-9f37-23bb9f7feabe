# 日历界面模块
import tkinter as tk
from tkinter import ttk
import calendar
from datetime import datetime, date
from typing import Callable, Optional
from data_manager import DataManager
from config import COLORS, MAIN_WINDOW_WIDTH, MAIN_WINDOW_HEIGHT


class CalendarGUI:
    """日历界面类"""
    
    def __init__(self, root: tk.Tk, data_manager: DataManager, 
                 on_date_click: Callable[[str], None] = None):
        self.root = root
        self.data_manager = data_manager
        self.on_date_click = on_date_click
        
        # 当前显示的年月
        today = datetime.now()
        self.current_year = today.year
        self.current_month = today.month
        
        # 界面组件
        self.main_frame = None
        self.header_frame = None
        self.calendar_frame = None
        self.date_buttons = {}
        
        self._setup_ui()
        self._update_calendar()
    
    def _setup_ui(self):
        """设置用户界面"""
        # 主框架
        self.main_frame = tk.Frame(self.root, bg=COLORS['light'])
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 头部框架（月份导航）
        self._setup_header()
        
        # 日历框架
        self._setup_calendar_frame()
    
    def _setup_header(self):
        """设置头部导航"""
        self.header_frame = tk.Frame(self.main_frame, bg=COLORS['light'])
        self.header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 上一月按钮
        prev_btn = tk.Button(
            self.header_frame, 
            text="◀", 
            font=("Arial", 14),
            bg=COLORS['primary'],
            fg=COLORS['white'],
            command=self._prev_month,
            width=3
        )
        prev_btn.pack(side=tk.LEFT)
        
        # 月份年份标签
        self.month_label = tk.Label(
            self.header_frame,
            text="",
            font=("Arial", 16, "bold"),
            bg=COLORS['light'],
            fg=COLORS['dark']
        )
        self.month_label.pack(side=tk.LEFT, expand=True)
        
        # 下一月按钮
        next_btn = tk.Button(
            self.header_frame,
            text="▶",
            font=("Arial", 14),
            bg=COLORS['primary'],
            fg=COLORS['white'],
            command=self._next_month,
            width=3
        )
        next_btn.pack(side=tk.RIGHT)
        
        # 今天按钮
        today_btn = tk.Button(
            self.header_frame,
            text="今天",
            font=("Arial", 12),
            bg=COLORS['secondary'],
            fg=COLORS['dark'],
            command=self._go_to_today
        )
        today_btn.pack(side=tk.RIGHT, padx=(0, 10))
    
    def _setup_calendar_frame(self):
        """设置日历框架"""
        self.calendar_frame = tk.Frame(self.main_frame, bg=COLORS['light'])
        self.calendar_frame.pack(fill=tk.BOTH, expand=True)
        
        # 星期标题
        weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        for i, day in enumerate(weekdays):
            label = tk.Label(
                self.calendar_frame,
                text=day,
                font=("Arial", 12, "bold"),
                bg=COLORS['primary'],
                fg=COLORS['white'],
                relief=tk.RAISED,
                borderwidth=1
            )
            label.grid(row=0, column=i, sticky="nsew", padx=1, pady=1)
        
        # 配置网格权重
        for i in range(7):
            self.calendar_frame.columnconfigure(i, weight=1)
        for i in range(7):  # 6行日期 + 1行星期标题
            self.calendar_frame.rowconfigure(i, weight=1)
    
    def _update_calendar(self):
        """更新日历显示"""
        # 更新月份标签
        month_name = f"{self.current_year}年{self.current_month}月"
        self.month_label.config(text=month_name)
        
        # 清除旧的日期按钮
        for button in self.date_buttons.values():
            button.destroy()
        self.date_buttons.clear()
        
        # 获取当月日历
        cal = calendar.monthcalendar(self.current_year, self.current_month)
        today = date.today()
        
        # 创建日期按钮
        for week_num, week in enumerate(cal, start=1):
            for day_num, day in enumerate(week):
                if day == 0:
                    continue
                
                # 创建日期字符串
                date_str = f"{self.current_year:04d}-{self.current_month:02d}-{day:02d}"
                
                # 检查是否有任务
                tasks = self.data_manager.get_tasks_by_date(date_str)
                has_tasks = len(tasks) > 0
                
                # 确定按钮颜色
                bg_color = COLORS['white']
                if date(self.current_year, self.current_month, day) == today:
                    bg_color = COLORS['today']
                elif has_tasks:
                    bg_color = COLORS['has_task']
                
                # 创建按钮
                button = tk.Button(
                    self.calendar_frame,
                    text=str(day),
                    font=("Arial", 12),
                    bg=bg_color,
                    fg=COLORS['dark'],
                    relief=tk.RAISED,
                    borderwidth=1,
                    command=lambda d=date_str: self._on_date_click(d)
                )
                button.grid(row=week_num, column=day_num, sticky="nsew", padx=1, pady=1)
                
                # 如果有任务，添加小圆点标识
                if has_tasks:
                    button.config(text=f"{day}●")
                
                self.date_buttons[date_str] = button
    
    def _prev_month(self):
        """上一月"""
        if self.current_month == 1:
            self.current_month = 12
            self.current_year -= 1
        else:
            self.current_month -= 1
        self._update_calendar()
    
    def _next_month(self):
        """下一月"""
        if self.current_month == 12:
            self.current_month = 1
            self.current_year += 1
        else:
            self.current_month += 1
        self._update_calendar()
    
    def _go_to_today(self):
        """跳转到今天"""
        today = datetime.now()
        self.current_year = today.year
        self.current_month = today.month
        self._update_calendar()
    
    def _on_date_click(self, date_str: str):
        """日期点击事件"""
        if self.on_date_click:
            self.on_date_click(date_str)
    
    def refresh_calendar(self):
        """刷新日历显示"""
        self._update_calendar()
    
    def highlight_date(self, date_str: str):
        """高亮指定日期"""
        if date_str in self.date_buttons:
            button = self.date_buttons[date_str]
            button.config(bg=COLORS['selected'])
            # 0.5秒后恢复原色
            self.root.after(500, lambda: self._restore_date_color(date_str))
    
    def _restore_date_color(self, date_str: str):
        """恢复日期原始颜色"""
        if date_str in self.date_buttons:
            button = self.date_buttons[date_str]
            
            # 重新计算颜色
            year, month, day = map(int, date_str.split('-'))
            today = date.today()
            tasks = self.data_manager.get_tasks_by_date(date_str)
            has_tasks = len(tasks) > 0
            
            bg_color = COLORS['white']
            if date(year, month, day) == today:
                bg_color = COLORS['today']
            elif has_tasks:
                bg_color = COLORS['has_task']
            
            button.config(bg=bg_color)
