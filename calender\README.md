# 日历任务管理器

一个基于Python和tkinter开发的简单易用的日历任务管理工具。

## 功能特点

### 📅 直观的日历界面
- 清晰的月历视图
- 今日高亮显示
- 有任务的日期显示圆点标识
- 支持月份切换和快速跳转到今天

### ✅ 任务管理
- 点击日期即可添加/编辑任务
- 支持任务标题、描述、时间设置
- 任务完成状态管理
- 任务列表查看和管理

### ⏰ 智能提醒
- 后台自动监控任务时间
- 到达设定时间自动弹出提醒
- 支持标记完成或稍后提醒
- 避免重复提醒已处理的任务

### 💾 数据持久化
- 使用JSON文件本地存储
- 自动保存任务数据
- 程序重启后数据不丢失

## 系统要求

- Python 3.6+
- tkinter（Python内置库）
- 支持Windows、macOS、Linux

## 安装和运行

### 1. 克隆或下载项目
```bash
git clone <repository-url>
cd calender
```

### 2. 直接运行
```bash
python main.py
```

### 3. 运行测试（可选）
```bash
python test_app.py
```

## 使用说明

### 基本操作

1. **查看日历**
   - 启动程序后会显示当前月份的日历
   - 今天的日期用蓝色背景标识
   - 有任务的日期会显示小圆点

2. **添加任务**
   - 点击任意日期打开任务编辑窗口
   - 填写任务标题（必填）
   - 设置提醒时间（可选，格式：HH:MM）
   - 添加任务描述（可选）
   - 点击"保存"按钮

3. **编辑任务**
   - 在任务编辑窗口的任务列表中选择要编辑的任务
   - 修改任务信息
   - 点击"保存"按钮

4. **删除任务**
   - 选择要删除的任务
   - 点击"删除"按钮
   - 确认删除操作

5. **任务提醒**
   - 设置了时间的任务会在指定时间弹出提醒
   - 可以选择"标记完成"或"稍后提醒"

### 快捷操作

- **月份导航**：使用左右箭头切换月份
- **回到今天**：点击"今天"按钮或使用菜单
- **查看所有任务**：菜单 → 任务 → 所有任务
- **刷新界面**：菜单 → 文件 → 刷新

## 文件结构

```
calender/
├── main.py              # 主程序入口
├── calendar_gui.py      # 日历界面模块
├── task_editor.py       # 任务编辑界面模块
├── data_manager.py      # 数据存储管理模块
├── reminder_service.py  # 提醒服务模块
├── config.py           # 配置文件
├── test_app.py         # 测试脚本
├── tasks.json          # 任务数据文件（自动生成）
└── README.md           # 说明文档
```

## 配置说明

可以在 `config.py` 文件中修改以下配置：

- **窗口大小**：`MAIN_WINDOW_WIDTH`、`MAIN_WINDOW_HEIGHT`
- **颜色主题**：`COLORS` 字典
- **提醒检查间隔**：`REMINDER_CHECK_INTERVAL`
- **日期时间格式**：`DATE_FORMAT`、`TIME_FORMAT`

## 打包为可执行文件

如果需要打包为exe文件，可以使用PyInstaller：

```bash
# 安装PyInstaller
pip install pyinstaller

# 打包为单个exe文件
pyinstaller --onefile --windowed main.py

# 打包后的文件在dist目录中
```

## 故障排除

### 常见问题

1. **程序无法启动**
   - 确保Python版本3.6+
   - 检查是否有tkinter库（通常内置）

2. **任务数据丢失**
   - 检查程序目录下是否有tasks.json文件
   - 确保程序有写入权限

3. **提醒不工作**
   - 检查任务时间格式是否正确（HH:MM）
   - 确保系统时间正确

4. **界面显示异常**
   - 尝试调整窗口大小
   - 重启程序

### 技术支持

如果遇到问题，请检查：
1. Python版本和依赖库
2. 系统权限设置
3. 防火墙或安全软件设置

## 开发说明

### 代码结构
- 采用模块化设计，便于维护和扩展
- 使用MVC模式分离界面和业务逻辑
- 支持多线程提醒服务

### 扩展功能
可以考虑添加的功能：
- 任务分类和标签
- 导入/导出功能
- 网络同步
- 更多提醒方式
- 主题切换

## 版本历史

- **v1.0.0** - 初始版本
  - 基本日历界面
  - 任务管理功能
  - 提醒服务
  - 数据持久化

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
