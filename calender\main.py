# 主程序入口
import tkinter as tk
from tkinter import messagebox
import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_manager import DataManager
from calendar_gui import CalendarGUI
from task_editor import TaskEditor
from reminder_service import ReminderService
from config import APP_NAME, MAIN_WINDOW_WIDTH, MAIN_WINDOW_HEIGHT, COLORS


class CalendarApp:
    """日历应用主类"""
    
    def __init__(self):
        self.root = None
        self.data_manager = None
        self.calendar_gui = None
        self.reminder_service = None
        
        self._setup_application()
    
    def _setup_application(self):
        """设置应用程序"""
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title(APP_NAME)
        self.root.geometry(f"{MAIN_WINDOW_WIDTH}x{MAIN_WINDOW_HEIGHT}")
        self.root.configure(bg=COLORS['light'])
        
        # 设置窗口图标（可选）
        try:
            # 如果有图标文件，可以在这里设置
            # self.root.iconbitmap('icon.ico')
            pass
        except:
            pass
        
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # 初始化数据管理器
        self.data_manager = DataManager()
        
        # 创建日历界面
        self.calendar_gui = CalendarGUI(
            self.root, 
            self.data_manager, 
            on_date_click=self._on_date_click
        )
        
        # 启动提醒服务
        self.reminder_service = ReminderService(self.data_manager)
        self.reminder_service.start()
        
        # 创建菜单栏
        self._create_menu()
        
        # 居中显示窗口
        self._center_window()
        
        print(f"{APP_NAME} 启动成功！")
    
    def _create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="刷新", command=self._refresh_calendar)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._on_closing)
        
        # 视图菜单
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="视图", menu=view_menu)
        view_menu.add_command(label="回到今天", command=self._go_to_today)
        view_menu.add_command(label="刷新日历", command=self._refresh_calendar)
        
        # 任务菜单
        task_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="任务", menu=task_menu)
        task_menu.add_command(label="今日任务", command=self._show_today_tasks)
        task_menu.add_command(label="所有任务", command=self._show_all_tasks)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self._show_help)
        help_menu.add_command(label="关于", command=self._show_about)
    
    def _center_window(self):
        """居中显示窗口"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_width() // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_height() // 2)
        self.root.geometry(f"+{x}+{y}")
    
    def _on_date_click(self, date_str: str):
        """日期点击事件处理"""
        print(f"点击日期: {date_str}")
        
        # 高亮选中的日期
        self.calendar_gui.highlight_date(date_str)
        
        # 打开任务编辑窗口
        task_editor = TaskEditor(
            self.root, 
            self.data_manager, 
            date_str,
            on_save=self._on_task_save
        )
    
    def _on_task_save(self):
        """任务保存后的回调"""
        # 刷新日历显示
        self.calendar_gui.refresh_calendar()
        print("任务已保存，日历已刷新")
    
    def _refresh_calendar(self):
        """刷新日历"""
        self.calendar_gui.refresh_calendar()
        messagebox.showinfo("提示", "日历已刷新")
    
    def _go_to_today(self):
        """跳转到今天"""
        self.calendar_gui._go_to_today()
    
    def _show_today_tasks(self):
        """显示今日任务"""
        today = datetime.now().strftime("%Y-%m-%d")
        self._on_date_click(today)
    
    def _show_all_tasks(self):
        """显示所有任务"""
        tasks = self.data_manager.get_all_tasks()
        
        # 创建任务列表窗口
        task_window = tk.Toplevel(self.root)
        task_window.title("所有任务")
        task_window.geometry("600x400")
        task_window.transient(self.root)
        
        # 创建任务列表
        frame = tk.Frame(task_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 滚动条
        scrollbar = tk.Scrollbar(frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 列表框
        listbox = tk.Listbox(frame, yscrollcommand=scrollbar.set, font=("Arial", 10))
        listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=listbox.yview)
        
        # 添加任务到列表
        for task in sorted(tasks, key=lambda t: (t.date, t.time or "00:00")):
            status = "✓" if task.completed else "○"
            time_str = task.time if task.time else "无时间"
            display_text = f"{task.date} {time_str} {status} {task.title}"
            listbox.insert(tk.END, display_text)
        
        if not tasks:
            listbox.insert(tk.END, "暂无任务")
    
    def _show_help(self):
        """显示帮助信息"""
        help_text = """
使用说明：

1. 日历界面：
   - 点击日期可以查看和编辑该日的任务
   - 有任务的日期会显示小圆点标识
   - 今天的日期会用蓝色背景标识
   - 使用左右箭头切换月份

2. 任务管理：
   - 在任务编辑窗口中可以添加、修改、删除任务
   - 设置任务时间后会自动提醒
   - 可以标记任务为已完成

3. 提醒功能：
   - 程序会在后台监控任务时间
   - 到达设定时间时会弹出提醒窗口
   - 可以选择标记完成或稍后提醒

4. 快捷操作：
   - 双击日期快速编辑任务
   - 使用菜单栏快速访问功能
        """
        
        messagebox.showinfo("使用说明", help_text)
    
    def _show_about(self):
        """显示关于信息"""
        about_text = f"""
{APP_NAME}

版本：1.0.0
开发者：Python开发者

这是一个简单易用的日历任务管理工具，
帮助您更好地管理日常任务和时间安排。

功能特点：
• 直观的日历界面
• 任务添加和编辑
• 自动提醒功能
• 数据本地存储
        """
        
        messagebox.showinfo("关于", about_text)
    
    def _on_closing(self):
        """程序关闭事件"""
        try:
            # 停止提醒服务
            if self.reminder_service:
                self.reminder_service.stop()
            
            # 保存数据
            if self.data_manager:
                self.data_manager.save_tasks()
            
            print(f"{APP_NAME} 正在关闭...")
            self.root.destroy()
            
        except Exception as e:
            print(f"关闭程序时出错: {e}")
            self.root.destroy()
    
    def run(self):
        """运行应用程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n程序被用户中断")
            self._on_closing()
        except Exception as e:
            print(f"程序运行出错: {e}")
            messagebox.showerror("错误", f"程序运行出错: {e}")


def main():
    """主函数"""
    try:
        app = CalendarApp()
        app.run()
    except Exception as e:
        print(f"启动程序失败: {e}")
        messagebox.showerror("启动错误", f"启动程序失败: {e}")


if __name__ == "__main__":
    main()
