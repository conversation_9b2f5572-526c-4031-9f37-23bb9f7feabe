# 测试脚本
import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_manager import DataManager, Task
import uuid


def test_data_manager():
    """测试数据管理器"""
    print("=== 测试数据管理器 ===")
    
    # 创建数据管理器
    dm = DataManager()
    
    # 创建测试任务
    today = datetime.now().strftime("%Y-%m-%d")
    tomorrow = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    
    test_tasks = [
        Task(
            task_id=str(uuid.uuid4()),
            title="测试任务1",
            description="这是第一个测试任务",
            date=today,
            time="09:00",
            completed=False
        ),
        Task(
            task_id=str(uuid.uuid4()),
            title="测试任务2",
            description="这是第二个测试任务",
            date=today,
            time="14:30",
            completed=True
        ),
        Task(
            task_id=str(uuid.uuid4()),
            title="明天的任务",
            description="这是明天的任务",
            date=tomorrow,
            time="10:00",
            completed=False
        )
    ]
    
    # 添加任务
    for task in test_tasks:
        dm.add_task(task)
        print(f"添加任务: {task.title}")
    
    # 查询今日任务
    today_tasks = dm.get_tasks_by_date(today)
    print(f"\n今日任务数量: {len(today_tasks)}")
    for task in today_tasks:
        status = "已完成" if task.completed else "未完成"
        print(f"- {task.title} ({task.time}) - {status}")
    
    # 查询所有任务
    all_tasks = dm.get_all_tasks()
    print(f"\n总任务数量: {len(all_tasks)}")
    
    # 测试更新任务
    if today_tasks:
        task_to_update = today_tasks[0]
        task_to_update.title = "更新后的任务标题"
        dm.update_task(task_to_update)
        print(f"\n更新任务: {task_to_update.title}")
    
    # 测试删除任务
    if len(all_tasks) > 1:
        task_to_delete = all_tasks[-1]
        dm.delete_task(task_to_delete.id)
        print(f"删除任务: {task_to_delete.title}")
    
    print("数据管理器测试完成！")


def test_reminder_logic():
    """测试提醒逻辑"""
    print("\n=== 测试提醒逻辑 ===")
    
    dm = DataManager()
    
    # 创建一个过期的任务（用于测试提醒）
    past_time = datetime.now() - timedelta(minutes=1)
    past_task = Task(
        task_id=str(uuid.uuid4()),
        title="过期任务测试",
        description="这个任务应该触发提醒",
        date=past_time.strftime("%Y-%m-%d"),
        time=past_time.strftime("%H:%M"),
        completed=False
    )
    
    dm.add_task(past_task)
    
    # 检查待提醒任务
    pending_reminders = dm.get_pending_reminders()
    print(f"待提醒任务数量: {len(pending_reminders)}")
    
    for task in pending_reminders:
        print(f"- {task.title} (应在 {task.date} {task.time} 提醒)")
    
    print("提醒逻辑测试完成！")


def create_sample_data():
    """创建示例数据"""
    print("\n=== 创建示例数据 ===")
    
    dm = DataManager()
    
    # 清除现有数据
    dm.tasks = []
    
    # 创建一周的示例任务
    base_date = datetime.now()
    
    sample_tasks = [
        # 今天
        ("会议：项目讨论", "09:00", "与团队讨论项目进展"),
        ("午餐约会", "12:30", "与朋友共进午餐"),
        ("健身", "18:00", "去健身房锻炼"),
        
        # 明天
        ("医生预约", "10:00", "定期体检", 1),
        ("购物", "15:00", "买生活用品", 1),
        
        # 后天
        ("读书", "20:00", "阅读技术书籍", 2),
        ("电影", "21:30", "看新上映的电影", 2),
    ]
    
    for i, task_info in enumerate(sample_tasks):
        if len(task_info) == 4:
            title, time, desc, day_offset = task_info
        else:
            title, time, desc = task_info
            day_offset = 0
        
        task_date = (base_date + timedelta(days=day_offset)).strftime("%Y-%m-%d")
        
        task = Task(
            task_id=str(uuid.uuid4()),
            title=title,
            description=desc,
            date=task_date,
            time=time,
            completed=False
        )
        
        dm.add_task(task)
        print(f"创建任务: {title} ({task_date} {time})")
    
    print("示例数据创建完成！")


def main():
    """主测试函数"""
    print("开始测试日历任务管理器...")
    
    try:
        # 测试数据管理器
        test_data_manager()
        
        # 测试提醒逻辑
        test_reminder_logic()
        
        # 创建示例数据
        create_sample_data()
        
        print("\n所有测试完成！现在可以运行 main.py 查看效果。")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
