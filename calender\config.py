# 配置文件
import os

# 应用配置
APP_NAME = "日历任务管理器"
APP_VERSION = "1.0.0"

# 窗口配置
MAIN_WINDOW_WIDTH = 800
MAIN_WINDOW_HEIGHT = 600
TASK_WINDOW_WIDTH = 400
TASK_WINDOW_HEIGHT = 300

# 颜色主题
COLORS = {
    'primary': '#2196F3',
    'secondary': '#FFC107',
    'success': '#4CAF50',
    'danger': '#F44336',
    'warning': '#FF9800',
    'info': '#00BCD4',
    'light': '#F8F9FA',
    'dark': '#212121',
    'white': '#FFFFFF',
    'today': '#E3F2FD',
    'selected': '#BBDEFB',
    'has_task': '#C8E6C9',
    'completed_task': '#E8F5E8',
    'urgent_task': '#FFEBEE',
    'border': '#E0E0E0',
    'hover': '#F0F0F0'
}

# 文件路径
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_FILE = os.path.join(BASE_DIR, 'tasks.json')

# 日期格式
DATE_FORMAT = "%Y-%m-%d"
TIME_FORMAT = "%H:%M"
DATETIME_FORMAT = "%Y-%m-%d %H:%M"

# 提醒设置
REMINDER_CHECK_INTERVAL = 60  # 秒
