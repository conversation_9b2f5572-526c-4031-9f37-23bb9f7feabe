# 提醒服务模块
import threading
import time
from datetime import datetime
from typing import Callable, List
import tkinter as tk
from tkinter import messagebox
from data_manager import DataManager, Task
from config import REMINDER_CHECK_INTERVAL


class ReminderService:
    """提醒服务"""
    
    def __init__(self, data_manager: DataManager):
        self.data_manager = data_manager
        self.running = False
        self.thread = None
        self.reminded_tasks = set()  # 已提醒的任务ID集合
        
    def start(self):
        """启动提醒服务"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._run_reminder_loop, daemon=True)
            self.thread.start()
            print("提醒服务已启动")
    
    def stop(self):
        """停止提醒服务"""
        self.running = False
        if self.thread:
            self.thread.join()
        print("提醒服务已停止")
    
    def _run_reminder_loop(self):
        """提醒循环"""
        while self.running:
            try:
                self._check_reminders()
                time.sleep(REMINDER_CHECK_INTERVAL)
            except Exception as e:
                print(f"提醒服务错误: {e}")
                time.sleep(REMINDER_CHECK_INTERVAL)
    
    def _check_reminders(self):
        """检查需要提醒的任务"""
        now = datetime.now()
        
        for task in self.data_manager.get_all_tasks():
            if (not task.completed and 
                task.id not in self.reminded_tasks and
                task.get_datetime()):
                
                task_datetime = task.get_datetime()
                if task_datetime and task_datetime <= now:
                    self._show_reminder(task)
                    self.reminded_tasks.add(task.id)
    
    def _show_reminder(self, task: Task):
        """显示提醒"""
        try:
            # 创建一个临时的根窗口来显示消息框
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            
            message = f"任务提醒：{task.title}"
            if task.description:
                message += f"\n描述：{task.description}"
            if task.time:
                message += f"\n时间：{task.time}"
            
            # 显示提醒对话框
            result = messagebox.askyesno(
                "任务提醒", 
                f"{message}\n\n是否标记为已完成？",
                parent=root
            )
            
            if result:
                # 标记任务为已完成
                task.completed = True
                self.data_manager.update_task(task)
            
            root.destroy()
            
        except Exception as e:
            print(f"显示提醒失败: {e}")
    
    def reset_reminded_tasks(self):
        """重置已提醒任务列表（用于测试或重新启动）"""
        self.reminded_tasks.clear()


class ReminderWindow:
    """提醒窗口类"""
    
    def __init__(self, task: Task, callback: Callable = None):
        self.task = task
        self.callback = callback
        self.window = None
        self._create_window()
    
    def _create_window(self):
        """创建提醒窗口"""
        self.window = tk.Toplevel()
        self.window.title("任务提醒")
        self.window.geometry("350x200")
        self.window.resizable(False, False)
        
        # 设置窗口置顶
        self.window.attributes('-topmost', True)
        
        # 任务信息
        info_frame = tk.Frame(self.window)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        tk.Label(info_frame, text="任务提醒", font=("Arial", 14, "bold")).pack(pady=(0, 10))
        
        tk.Label(info_frame, text=f"任务：{self.task.title}", 
                font=("Arial", 12)).pack(anchor=tk.W)
        
        if self.task.description:
            tk.Label(info_frame, text=f"描述：{self.task.description}", 
                    font=("Arial", 10)).pack(anchor=tk.W, pady=(5, 0))
        
        if self.task.time:
            tk.Label(info_frame, text=f"时间：{self.task.time}", 
                    font=("Arial", 10)).pack(anchor=tk.W, pady=(5, 0))
        
        # 按钮
        button_frame = tk.Frame(self.window)
        button_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        tk.Button(button_frame, text="标记完成", 
                 command=self._mark_completed).pack(side=tk.LEFT, padx=(0, 10))
        
        tk.Button(button_frame, text="稍后提醒", 
                 command=self._snooze).pack(side=tk.LEFT, padx=(0, 10))
        
        tk.Button(button_frame, text="关闭", 
                 command=self._close).pack(side=tk.RIGHT)
        
        # 居中显示
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
    
    def _mark_completed(self):
        """标记任务为已完成"""
        if self.callback:
            self.callback(self.task, 'completed')
        self._close()
    
    def _snooze(self):
        """稍后提醒"""
        if self.callback:
            self.callback(self.task, 'snooze')
        self._close()
    
    def _close(self):
        """关闭窗口"""
        if self.window:
            self.window.destroy()
