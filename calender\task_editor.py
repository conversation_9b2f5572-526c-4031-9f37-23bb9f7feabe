# 任务编辑界面模块
import tkinter as tk
from tkinter import ttk, messagebox
import uuid
from datetime import datetime
from typing import Callable, Optional, List
from data_manager import DataManager, Task
from config import COLORS, TASK_WINDOW_WIDTH, TASK_WINDOW_HEIGHT, TIME_FORMAT


class TaskEditor:
    """任务编辑器"""
    
    def __init__(self, parent: tk.Tk, data_manager: DataManager, 
                 selected_date: str, on_save: Callable = None):
        self.parent = parent
        self.data_manager = data_manager
        self.selected_date = selected_date
        self.on_save = on_save
        
        self.window = None
        self.current_task = None
        
        # 界面组件
        self.title_var = tk.StringVar()
        self.description_text = None
        self.time_var = tk.StringVar()
        self.completed_var = tk.BooleanVar()
        self.task_listbox = None
        
        self._create_window()
        self._load_tasks()
    
    def _create_window(self):
        """创建编辑窗口"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(f"任务管理 - {self.selected_date}")
        self.window.geometry(f"{TASK_WINDOW_WIDTH + 200}x{TASK_WINDOW_HEIGHT + 300}")
        self.window.resizable(True, True)
        
        # 设置窗口图标和属性
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # 创建主框架
        main_frame = tk.Frame(self.window, bg=COLORS['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建任务列表区域
        self._create_task_list(main_frame)
        
        # 创建分隔线
        separator = ttk.Separator(main_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=10)
        
        # 创建任务编辑区域
        self._create_task_form(main_frame)
        
        # 创建按钮区域
        self._create_buttons(main_frame)
        
        # 居中显示窗口
        self._center_window()
    
    def _create_task_list(self, parent):
        """创建任务列表"""
        list_frame = tk.LabelFrame(parent, text="当日任务", bg=COLORS['light'], 
                                  font=("Arial", 12, "bold"))
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建列表框和滚动条
        list_container = tk.Frame(list_frame, bg=COLORS['light'])
        list_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        scrollbar = tk.Scrollbar(list_container)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.task_listbox = tk.Listbox(
            list_container,
            yscrollcommand=scrollbar.set,
            font=("Arial", 10),
            bg=COLORS['white'],
            selectmode=tk.SINGLE
        )
        self.task_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.task_listbox.yview)
        
        # 绑定选择事件
        self.task_listbox.bind('<<ListboxSelect>>', self._on_task_select)
        self.task_listbox.bind('<Double-Button-1>', self._on_task_double_click)
    
    def _create_task_form(self, parent):
        """创建任务表单"""
        form_frame = tk.LabelFrame(parent, text="任务详情", bg=COLORS['light'],
                                  font=("Arial", 12, "bold"))
        form_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 任务标题
        title_frame = tk.Frame(form_frame, bg=COLORS['light'])
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(title_frame, text="标题:", bg=COLORS['light'], 
                font=("Arial", 10)).pack(side=tk.LEFT)
        title_entry = tk.Entry(title_frame, textvariable=self.title_var, 
                              font=("Arial", 10))
        title_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))
        
        # 任务时间
        time_frame = tk.Frame(form_frame, bg=COLORS['light'])
        time_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(time_frame, text="时间:", bg=COLORS['light'], 
                font=("Arial", 10)).pack(side=tk.LEFT)
        time_entry = tk.Entry(time_frame, textvariable=self.time_var, 
                             font=("Arial", 10), width=10)
        time_entry.pack(side=tk.LEFT, padx=(10, 0))
        
        tk.Label(time_frame, text="(格式: HH:MM)", bg=COLORS['light'], 
                font=("Arial", 8), fg=COLORS['dark']).pack(side=tk.LEFT, padx=(5, 0))
        
        # 完成状态
        completed_frame = tk.Frame(form_frame, bg=COLORS['light'])
        completed_frame.pack(fill=tk.X, padx=10, pady=5)
        
        completed_check = tk.Checkbutton(
            completed_frame, 
            text="已完成", 
            variable=self.completed_var,
            bg=COLORS['light'],
            font=("Arial", 10)
        )
        completed_check.pack(side=tk.LEFT)
        
        # 任务描述
        desc_frame = tk.Frame(form_frame, bg=COLORS['light'])
        desc_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        tk.Label(desc_frame, text="描述:", bg=COLORS['light'], 
                font=("Arial", 10)).pack(anchor=tk.W)
        
        desc_container = tk.Frame(desc_frame, bg=COLORS['light'])
        desc_container.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        desc_scrollbar = tk.Scrollbar(desc_container)
        desc_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.description_text = tk.Text(
            desc_container,
            height=4,
            font=("Arial", 10),
            yscrollcommand=desc_scrollbar.set,
            bg=COLORS['white']
        )
        self.description_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        desc_scrollbar.config(command=self.description_text.yview)
    
    def _create_buttons(self, parent):
        """创建按钮"""
        button_frame = tk.Frame(parent, bg=COLORS['light'])
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 新建任务按钮
        new_btn = tk.Button(
            button_frame,
            text="新建任务",
            font=("Arial", 10),
            bg=COLORS['success'],
            fg=COLORS['white'],
            command=self._new_task
        )
        new_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 保存按钮
        save_btn = tk.Button(
            button_frame,
            text="保存",
            font=("Arial", 10),
            bg=COLORS['primary'],
            fg=COLORS['white'],
            command=self._save_task
        )
        save_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 删除按钮
        delete_btn = tk.Button(
            button_frame,
            text="删除",
            font=("Arial", 10),
            bg=COLORS['danger'],
            fg=COLORS['white'],
            command=self._delete_task
        )
        delete_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 关闭按钮
        close_btn = tk.Button(
            button_frame,
            text="关闭",
            font=("Arial", 10),
            bg=COLORS['secondary'],
            fg=COLORS['dark'],
            command=self._close_window
        )
        close_btn.pack(side=tk.RIGHT)
    
    def _center_window(self):
        """居中显示窗口"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
    
    def _load_tasks(self):
        """加载当日任务"""
        self.task_listbox.delete(0, tk.END)
        tasks = self.data_manager.get_tasks_by_date(self.selected_date)
        
        for task in tasks:
            status = "✓" if task.completed else "○"
            time_str = task.time if task.time else "无时间"
            display_text = f"{status} {time_str} - {task.title}"
            self.task_listbox.insert(tk.END, display_text)
            
        # 如果没有任务，显示提示
        if not tasks:
            self.task_listbox.insert(tk.END, "暂无任务，点击'新建任务'添加")
    
    def _on_task_select(self, event):
        """任务选择事件"""
        selection = self.task_listbox.curselection()
        if selection:
            index = selection[0]
            tasks = self.data_manager.get_tasks_by_date(self.selected_date)
            if 0 <= index < len(tasks):
                self._load_task_to_form(tasks[index])
    
    def _on_task_double_click(self, event):
        """任务双击事件"""
        self._on_task_select(event)
    
    def _load_task_to_form(self, task: Task):
        """加载任务到表单"""
        self.current_task = task
        self.title_var.set(task.title)
        self.time_var.set(task.time)
        self.completed_var.set(task.completed)
        
        self.description_text.delete(1.0, tk.END)
        self.description_text.insert(1.0, task.description)
    
    def _new_task(self):
        """新建任务"""
        self.current_task = None
        self.title_var.set("")
        self.time_var.set("")
        self.completed_var.set(False)
        self.description_text.delete(1.0, tk.END)
    
    def _save_task(self):
        """保存任务"""
        title = self.title_var.get().strip()
        if not title:
            messagebox.showwarning("警告", "请输入任务标题")
            return
        
        time_str = self.time_var.get().strip()
        if time_str:
            # 验证时间格式
            try:
                datetime.strptime(time_str, TIME_FORMAT)
            except ValueError:
                messagebox.showerror("错误", "时间格式不正确，请使用 HH:MM 格式")
                return
        
        description = self.description_text.get(1.0, tk.END).strip()
        completed = self.completed_var.get()
        
        if self.current_task:
            # 更新现有任务
            self.current_task.title = title
            self.current_task.description = description
            self.current_task.time = time_str
            self.current_task.completed = completed
            self.data_manager.update_task(self.current_task)
        else:
            # 创建新任务
            task_id = str(uuid.uuid4())
            new_task = Task(
                task_id=task_id,
                title=title,
                description=description,
                date=self.selected_date,
                time=time_str,
                completed=completed
            )
            self.data_manager.add_task(new_task)
            self.current_task = new_task
        
        self._load_tasks()
        if self.on_save:
            self.on_save()
        
        messagebox.showinfo("成功", "任务保存成功")
    
    def _delete_task(self):
        """删除任务"""
        if not self.current_task:
            messagebox.showwarning("警告", "请先选择要删除的任务")
            return
        
        result = messagebox.askyesno("确认", f"确定要删除任务 '{self.current_task.title}' 吗？")
        if result:
            self.data_manager.delete_task(self.current_task.id)
            self._new_task()
            self._load_tasks()
            if self.on_save:
                self.on_save()
            messagebox.showinfo("成功", "任务删除成功")
    
    def _close_window(self):
        """关闭窗口"""
        self.window.destroy()
