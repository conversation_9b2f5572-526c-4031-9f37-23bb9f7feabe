# 数据管理模块
import json
import os
from datetime import datetime
from typing import List, Dict, Optional
from config import DATA_FILE, DATE_FORMAT, DATETIME_FORMAT


class Task:
    """任务类"""
    def __init__(self, task_id: str, title: str, description: str = "", 
                 date: str = "", time: str = "", completed: bool = False):
        self.id = task_id
        self.title = title
        self.description = description
        self.date = date  # YYYY-MM-DD格式
        self.time = time  # HH:MM格式
        self.completed = completed
        
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'date': self.date,
            'time': self.time,
            'completed': self.completed
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Task':
        """从字典创建任务"""
        return cls(
            task_id=data.get('id', ''),
            title=data.get('title', ''),
            description=data.get('description', ''),
            date=data.get('date', ''),
            time=data.get('time', ''),
            completed=data.get('completed', False)
        )
    
    def get_datetime(self) -> Optional[datetime]:
        """获取任务的完整日期时间"""
        if self.date and self.time:
            try:
                datetime_str = f"{self.date} {self.time}"
                return datetime.strptime(datetime_str, DATETIME_FORMAT)
            except ValueError:
                return None
        return None


class DataManager:
    """数据管理器"""
    
    def __init__(self):
        self.tasks: List[Task] = []
        self.load_tasks()
    
    def load_tasks(self):
        """从文件加载任务"""
        try:
            if os.path.exists(DATA_FILE):
                with open(DATA_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.tasks = [Task.from_dict(task_data) for task_data in data]
            else:
                self.tasks = []
        except Exception as e:
            print(f"加载任务失败: {e}")
            self.tasks = []
    
    def save_tasks(self):
        """保存任务到文件"""
        try:
            with open(DATA_FILE, 'w', encoding='utf-8') as f:
                json.dump([task.to_dict() for task in self.tasks], f, 
                         ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存任务失败: {e}")
    
    def add_task(self, task: Task):
        """添加任务"""
        self.tasks.append(task)
        self.save_tasks()
    
    def update_task(self, task: Task):
        """更新任务"""
        for i, existing_task in enumerate(self.tasks):
            if existing_task.id == task.id:
                self.tasks[i] = task
                self.save_tasks()
                return True
        return False
    
    def delete_task(self, task_id: str):
        """删除任务"""
        self.tasks = [task for task in self.tasks if task.id != task_id]
        self.save_tasks()
    
    def get_tasks_by_date(self, date: str) -> List[Task]:
        """获取指定日期的任务"""
        return [task for task in self.tasks if task.date == date]
    
    def get_all_tasks(self) -> List[Task]:
        """获取所有任务"""
        return self.tasks.copy()
    
    def get_pending_reminders(self) -> List[Task]:
        """获取需要提醒的任务"""
        now = datetime.now()
        pending_tasks = []
        
        for task in self.tasks:
            if not task.completed:
                task_datetime = task.get_datetime()
                if task_datetime and task_datetime <= now:
                    pending_tasks.append(task)
        
        return pending_tasks
